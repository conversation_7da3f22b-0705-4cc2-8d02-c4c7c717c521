import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    StarIcon,
    HomeIcon,
    UserIcon,
    CalendarIcon,
    CheckCircleIcon,
    ClockIcon,
    XCircleIcon,
    MagnifyingGlassIcon,
    ChatBubbleLeftIcon,
    EyeIcon,
} from '@heroicons/react/24/outline';

interface Review {
    id: number;
    rating: number;
    comment: string;
    is_approved: boolean;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
    };
    property: {
        id: number;
        title: string;
        city: string;
        primary_image?: {
            image_path: string;
        };
    };
}

interface ReviewsPageProps extends PageProps {
    reviews: {
        data: Review[];
        links: any[];
        meta: any;
    };
}

export default function Reviews({ auth, reviews }: ReviewsPageProps) {
    // Fallback for undefined reviews
    const safeReviews = reviews || { data: [], meta: { total: 0 }, links: [] };

    const handleViewProperty = (propertyId: number) => {
        window.location.href = `/properties/${propertyId}`;
    };

    const handleReplyToReview = (userId: number, propertyId: number) => {
        // This would typically open a messaging modal or navigate to a chat page
        console.log(`Reply to user ${userId} about property ${propertyId}`);
        // Example: router.visit(`/landlord/messages?user_id=${userId}&property_id=${propertyId}`);
    };

    const handleUpdateReviewStatus = async (reviewId: number, approve: boolean) => {
        const action = approve ? 'approve' : 'reject';
        if (confirm(`Are you sure you want to ${action} this review?`)) {
            try {
                const response = await fetch(`/api/reviews/${reviewId}/${action}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                });

                if (response.ok) {
                    window.location.reload(); // Reload to reflect changes
                } else {
                    const errorData = await response.json();
                    alert(`Failed to ${action} review: ${errorData.message || response.statusText}`);
                }
            } catch (error) {
                console.error(`Error ${action}ing review:`, error);
                alert(`An error occurred while ${action}ing the review.`);
            }
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const getStatusColor = (isApproved: boolean) => {
        return isApproved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
    };

    const getStatusIcon = (isApproved: boolean) => {
        return isApproved ?
            <CheckCircleIcon className="h-5 w-5 text-green-600" /> :
            <ClockIcon className="h-5 w-5 text-yellow-600" />;
    };

    const renderStars = (rating: number) => {
        return (
            <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                    <StarIcon
                        key={star}
                        className={`h-4 w-4 ${
                            star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                        }`}
                    />
                ))}
                <span className="ml-2 text-sm text-gray-600">({rating}/5)</span>
            </div>
        );
    };

    const getReviewStats = () => {
        const total = safeReviews.data.length;
        const approved = safeReviews.data.filter(r => r.is_approved).length;
        const pending = safeReviews.data.filter(r => !r.is_approved).length;
        const averageRating = total > 0 ?
            safeReviews.data.reduce((sum, review) => sum + review.rating, 0) / total : 0;

        return { total, approved, pending, averageRating };
    };

    const stats = getReviewStats();

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Property Reviews</h1>
                        <p className="text-gray-600">Reviews for your properties</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search reviews..."
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                        <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>All Reviews</option>
                            <option>Approved</option>
                            <option>Pending</option>
                        </select>
                    </div>
                </div>
            }
        >
            <Head title="Property Reviews - Landlord" />

            <div className="space-y-6">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-yellow-100">
                                <StarIcon className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Reviews</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-green-100">
                                <CheckCircleIcon className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Approved</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-yellow-100">
                                <ClockIcon className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Pending</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-100">
                                <StarIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Avg. Rating</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.averageRating.toFixed(1)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Reviews List */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">Recent Reviews</h3>
                    </div>
                    <div className="divide-y divide-gray-200">
                        {safeReviews.data.map((review) => (
                            <div key={review.id} className="p-6 hover:bg-gray-50">
                                <div className="flex items-start justify-between">
                                    <div className="flex items-start space-x-4 flex-1">
                                        <div className="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                                            {review.property.primary_image ? (
                                                <img
                                                    src={`/storage/${review.property.primary_image.image_path}`}
                                                    alt={review.property.title}
                                                    className="h-16 w-16 rounded-lg object-cover"
                                                />
                                            ) : (
                                                <HomeIcon className="h-8 w-8 text-gray-400" />
                                            )}
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <h4 className="text-lg font-medium text-gray-900">{review.property.title}</h4>
                                                    <p className="text-sm text-gray-500">{review.property.city}</p>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    {getStatusIcon(review.is_approved)}
                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(review.is_approved)}`}>
                                                        {review.is_approved ? 'Approved' : 'Pending'}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="mt-2 flex items-center justify-between">
                                                <div className="flex items-center space-x-4">
                                                    <div className="flex items-center">
                                                        <UserIcon className="h-4 w-4 text-gray-400 mr-1" />
                                                        <span className="text-sm text-gray-600">{review.user.name}</span>
                                                    </div>
                                                    {renderStars(review.rating)}
                                                </div>
                                            </div>
                                            <div className="mt-3">
                                                <p className="text-gray-700">{review.comment}</p>
                                            </div>
                                            <div className="mt-3 flex items-center justify-between">
                                                <div className="flex items-center text-sm text-gray-500">
                                                    <CalendarIcon className="h-4 w-4 mr-1" />
                                                    {formatDate(review.created_at)}
                                                </div>
                                                <div className="flex space-x-2">
                                                    <button
                                                        onClick={() => handleViewProperty(review.property.id)}
                                                        className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                                                    >
                                                        <EyeIcon className="h-4 w-4 inline mr-1" />
                                                        View Property
                                                    </button>
                                                    <button
                                                        onClick={() => handleReplyToReview(review.user.id, review.property.id)}
                                                        className="text-purple-600 hover:text-purple-900 text-sm font-medium"
                                                    >
                                                        <ChatBubbleLeftIcon className="h-4 w-4 inline mr-1" />
                                                        Reply
                                                    </button>
                                                    {!review.is_approved && (
                                                        <>
                                                            <button
                                                                onClick={() => handleUpdateReviewStatus(review.id, true)}
                                                                className="text-green-600 hover:text-green-900 text-sm font-medium"
                                                            >
                                                                Approve
                                                            </button>
                                                            <button
                                                                onClick={() => handleUpdateReviewStatus(review.id, false)}
                                                                className="text-red-600 hover:text-red-900 text-sm font-medium"
                                                            >
                                                                Reject
                                                            </button>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {safeReviews.data.length === 0 && (
                    <div className="text-center py-12">
                        <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews yet</h3>
                        <p className="mt-1 text-sm text-gray-500">Reviews from guests will appear here once they start reviewing your properties.</p>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}
