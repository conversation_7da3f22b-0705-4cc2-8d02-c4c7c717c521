import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    HomeIcon,
    PlusIcon,
    EyeIcon,
    PencilIcon,
    TrashIcon,
    StarIcon,
    HeartIcon,
    CalendarIcon,
} from '@heroicons/react/24/outline';

interface Property {
    id: number;
    title: string;
    type: string;
    price: number;
    city: string;
    status: string;
    views: number;
    created_at: string;
    primary_image?: {
        image_path: string;
    };
    bookings_count: number;
    favorites_count: number;
    reviews_count: number;
    reviews_avg_rating: number;
}

interface PropertiesPageProps extends PageProps {
    properties: {
        data: Property[];
        links: any[];
        meta: any;
    };
}

export default function Properties({ auth, properties }: PropertiesPageProps) {
    // Fallback for undefined properties
    const safeProperties = properties || { data: [], meta: { total: 0 }, links: [] };

    const handleDeleteProperty = async (propertyId: number) => {
        if (confirm('Are you sure you want to delete this property? This action cannot be undone.')) {
            try {
                const response = await fetch(`/api/properties/${propertyId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                });

                if (response.ok) {
                    // Reload the page or update the properties list
                    window.location.reload();
                } else {
                    const errorData = await response.json();
                    alert(`Failed to delete property: ${errorData.message || response.statusText}`);
                }
            } catch (error) {
                console.error('Error deleting property:', error);
                alert('An error occurred while deleting the property.');
            }
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'draft':
                return 'bg-yellow-100 text-yellow-800';
            case 'inactive':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">My Properties</h1>
                        <p className="text-gray-600">Manage your property listings</p>
                    </div>
                    <Link
                        href="/properties/create"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center hover:scale-105 active:scale-95 transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Property
                    </Link>
                </div>
            }
        >
            <Head title="My Properties - Landlord" />

            <div className="space-y-6">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-100">
                                <HomeIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Properties</p>
                                <p className="text-2xl font-bold text-gray-900">{safeProperties.meta.total}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Properties Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {safeProperties.data.map((property) => (
                        <div key={property.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg relative">
                                {property.primary_image ? (
                                    <img
                                        src={`/storage/${property.primary_image.image_path}`}
                                        alt={property.title}
                                        className="w-full h-48 object-cover rounded-t-lg"
                                    />
                                ) : (
                                    <div className="w-full h-48 bg-gray-300 flex items-center justify-center rounded-t-lg">
                                        <HomeIcon className="h-12 w-12 text-gray-400" />
                                    </div>
                                )}
                                <div className={`absolute top-2 right-2 px-2 py-1 text-xs rounded-full font-medium ${getStatusColor(property.status)}`}>
                                    {property.status}
                                </div>
                            </div>
                            <div className="p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">{property.title}</h3>
                                <p className="text-sm text-gray-500 capitalize mb-2">{property.type} • {property.city}</p>
                                <p className="text-xl font-bold text-gray-900 mb-4">{formatCurrency(property.price)}</p>

                                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <div className="flex items-center">
                                        <EyeIcon className="h-4 w-4 mr-1" />
                                        {property.views}
                                    </div>
                                    <div className="flex items-center">
                                        <CalendarIcon className="h-4 w-4 mr-1" />
                                        {property.bookings_count}
                                    </div>
                                    <div className="flex items-center">
                                        <HeartIcon className="h-4 w-4 mr-1" />
                                        {property.favorites_count}
                                    </div>
                                    <div className="flex items-center">
                                        <StarIcon className="h-4 w-4 mr-1" />
                                        {property.reviews_avg_rating ? property.reviews_avg_rating.toFixed(1) : '0.0'}
                                    </div>
                                </div>

                                <div className="flex space-x-2">
                                    <Link
                                        href={`/properties/${property.id}`}
                                        className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors text-center"
                                    >
                                        <EyeIcon className="h-4 w-4 inline mr-1" />
                                        View
                                    </Link>
                                    <Link
                                        href={`/properties/${property.id}/edit`}
                                        className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700 transition-colors text-center"
                                    >
                                        <PencilIcon className="h-4 w-4 inline mr-1" />
                                        Edit
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteProperty(property.id)}
                                        className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700 transition-colors"
                                    >
                                        <TrashIcon className="h-4 w-4" />
                                    </button>
                                </div>

                                <p className="text-xs text-gray-400 mt-3">Added {formatDate(property.created_at)}</p>
                            </div>
                        </div>
                    ))}
                </div>

                {safeProperties.data.length === 0 && (
                    <div className="text-center py-12">
                        <HomeIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No properties</h3>
                        <p className="mt-1 text-sm text-gray-500">Get started by creating your first property listing.</p>
                        <div className="mt-6">
                            <Link
                                href="/properties/create"
                                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            >
                                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                                Add Property
                            </Link>
                        </div>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}
