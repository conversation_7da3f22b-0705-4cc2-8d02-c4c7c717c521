<?php

namespace App\Http\Controllers;

use App\Models\Property;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Message;
use App\Models\Review;
use App\Models\Favorite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class LandlordDashboardController extends Controller
{


    /**
     * Display the landlord dashboard
     */
    public function index()
    {
        $userId = Auth::id();

        // Get dashboard statistics
        $stats = [
            'total_properties' => Property::where('user_id', $userId)->count(),
            'active_properties' => Property::where('user_id', $userId)
                ->where('status', 'published')->count(),
            'total_bookings' => Booking::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->count(),
            'pending_bookings' => Booking::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'pending')->count(),
            'total_revenue' => Payment::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'paid')->sum('amount'),
            'monthly_revenue' => Payment::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'paid')
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount'),
            'unread_messages' => Message::where('receiver_id', $userId)
                ->where('is_read', false)->count(),
            'pending_reviews' => Review::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('is_approved', false)->count(),
        ];

        // Property performance
        $properties = Property::where('user_id', $userId)
            ->withCount(['bookings', 'favorites', 'reviews'])
            ->withAvg('reviews', 'rating')
            ->with('primaryImage')
            ->latest()
            ->take(6)
            ->get();

        // Monthly revenue chart (last 6 months)
        $monthlyRevenue = Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->whereHas('property', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        })
        ->where('status', 'paid')
        ->where('paid_at', '>=', now()->subMonths(6))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        // Recent activities
        $recentBookings = Booking::with(['user', 'property'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->latest()->take(5)->get();

        $recentPayments = Payment::with(['user', 'property'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->latest()->take(5)->get();

        $recentMessages = Message::with(['sender', 'property'])
            ->where('receiver_id', $userId)
            ->latest()->take(5)->get();

        return Inertia::render('Landlord/Dashboard', [
            'stats' => $stats,
            'properties' => $properties,
            'monthlyRevenue' => $monthlyRevenue,
            'recentBookings' => $recentBookings,
            'recentPayments' => $recentPayments,
            'recentMessages' => $recentMessages,
        ]);
    }

    /**
     * Display properties management page
     */
    public function properties()
    {
        try {
            $userId = Auth::id();
            $properties = Property::where('user_id', $userId)
                ->withCount(['bookings', 'favorites', 'reviews'])
                ->withAvg('reviews', 'rating')
                ->with('primaryImage')
                ->latest()
                ->paginate(12);

            Log::info('Landlord properties data:', ['properties' => $properties->toArray()]);
            return Inertia::render('Landlord/Properties', [
                'properties' => $properties,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching landlord properties: ' . $e->getMessage(), ['exception' => $e]);
            $emptyPagination = new \Illuminate\Pagination\LengthAwarePaginator(
                [],
                0,
                12,
                1,
                ['path' => request()->url()]
            );

            return Inertia::render('Landlord/Properties', [
                'properties' => $emptyPagination,
            ]);
        }
    }

    /**
     * Display bookings management page
     */
    public function bookings()
    {
        $userId = Auth::id();
        $bookings = Booking::whereHas('property', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        })->with(['user', 'property'])
        ->latest()
        ->paginate(20);

        return Inertia::render('Landlord/Bookings', [
            'bookings' => $bookings,
        ]);
    }

    /**
     * Display payments page
     */
    public function payments()
    {
        $userId = Auth::id();
        $payments = Payment::whereHas('property', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        })->with(['user', 'property'])
        ->latest()
        ->paginate(20);

        return Inertia::render('Landlord/Payments', [
            'payments' => $payments,
        ]);
    }

    /**
     * Display messages page
     */
    public function messages()
    {
        $userId = Auth::id();
        $messages = Message::where('receiver_id', $userId)
            ->orWhere('sender_id', $userId)
            ->with(['sender', 'receiver', 'property'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Landlord/Messages', [
            'messages' => $messages,
        ]);
    }

    /**
     * Display reviews page
     */
    public function reviews()
    {
        $userId = Auth::id();
        $reviews = Review::whereHas('property', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        })->with(['user', 'property'])
        ->latest()
        ->paginate(20);

        return Inertia::render('Landlord/Reviews', [
            'reviews' => $reviews,
        ]);
    }

    /**
     * Display analytics page
     */
    public function analytics()
    {
        $userId = Auth::id();

        // Analytics data
        $analytics = [
            'property_views' => Property::where('user_id', $userId)->sum('views'),
            'total_bookings' => Booking::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->count(),
            'conversion_rate' => 0, // Calculate based on views vs bookings
            'total_revenue' => Payment::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'paid')->sum('amount'),
            'average_rating' => Review::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->avg('rating') ?? 0,
            'total_favorites' => Favorite::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->count(),
        ];

        return Inertia::render('Landlord/Analytics', [
            'analytics' => $analytics,
        ]);
    }
}
