import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    CurrencyDollarIcon,
    UserIcon,
    HomeIcon,
    CalendarIcon,
    CheckCircleIcon,
    ClockIcon,
    XCircleIcon,
    MagnifyingGlassIcon,
    ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';

interface Payment {
    id: number;
    amount: number;
    status: string;
    payment_type: string;
    payment_method: string;
    paid_at: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
    };
    property: {
        id: number;
        title: string;
        city: string;
    };
}

interface PaymentsPageProps extends PageProps {
    payments: {
        data: Payment[];
        links: any[];
        meta: any;
    };
}

export default function Payments({ auth, payments }: PaymentsPageProps) {
    // Fallback for undefined payments
    const safePayments = payments || { data: [], meta: { total: 0 }, links: [] };

    const handleViewPayment = (paymentId: number) => {
        console.log('View payment:', paymentId);
        // Implement navigation to a detailed payment view or open a modal
    };

    const handleGenerateReceipt = async (paymentId: number) => {
        try {
            const response = await fetch(`/api/payments/${paymentId}/receipt`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `receipt-${paymentId}.pdf`;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            } else {
                const errorData = await response.json();
                alert(`Failed to generate receipt: ${errorData.message || response.statusText}`);
            }
        } catch (error) {
            console.error('Error generating receipt:', error);
            alert('An error occurred while generating the receipt.');
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'paid':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            case 'refunded':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'paid':
                return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
            case 'pending':
                return <ClockIcon className="h-5 w-5 text-yellow-600" />;
            case 'failed':
                return <XCircleIcon className="h-5 w-5 text-red-600" />;
            case 'refunded':
                return <CheckCircleIcon className="h-5 w-5 text-blue-600" />;
            default:
                return <ClockIcon className="h-5 w-5 text-gray-600" />;
        }
    };

    const getPaymentStats = () => {
        const total = safePayments.data.length;
        const totalAmount = safePayments.data.reduce((sum, payment) => sum + payment.amount, 0);
        const paidAmount = safePayments.data
            .filter(p => p.status === 'paid')
            .reduce((sum, payment) => sum + payment.amount, 0);
        const pendingAmount = safePayments.data
            .filter(p => p.status === 'pending')
            .reduce((sum, payment) => sum + payment.amount, 0);

        return { total, totalAmount, paidAmount, pendingAmount };
    };

    const stats = getPaymentStats();

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">My Payments</h1>
                        <p className="text-gray-600">Track payments from your properties</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search payments..."
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                            Export
                        </button>
                    </div>
                </div>
            }
        >
            <Head title="My Payments - Landlord" />

            <div className="space-y-6">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-green-100">
                                <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Payments</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-100">
                                <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalAmount)}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-green-100">
                                <CheckCircleIcon className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Paid Amount</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.paidAmount)}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-yellow-100">
                                <ClockIcon className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Pending Amount</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.pendingAmount)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Payments Table */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">Recent Payments</h3>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Property & Tenant
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Payment Method
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {safePayments.data.map((payment) => (
                                    <tr key={payment.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                                                    <HomeIcon className="h-6 w-6 text-gray-400" />
                                                </div>
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{payment.property.title}</div>
                                                    <div className="text-sm text-gray-500">{payment.property.city}</div>
                                                    <div className="text-sm text-gray-500 flex items-center mt-1">
                                                        <UserIcon className="h-4 w-4 mr-1" />
                                                        {payment.user.name}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">{formatCurrency(payment.amount)}</div>
                                            <div className="text-sm text-gray-500 capitalize">{payment.payment_type}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                {getStatusIcon(payment.status)}
                                                <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                                                    {payment.status}
                                                </span>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                                            {payment.payment_method || 'N/A'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                {payment.paid_at ? formatDate(payment.paid_at) : formatDate(payment.created_at)}
                                            </div>
                                            <div className="text-sm text-gray-500 flex items-center">
                                                <CalendarIcon className="h-4 w-4 mr-1" />
                                                Created {formatDate(payment.created_at)}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex space-x-2">
                                                    <button
                                                        onClick={() => handleViewPayment(payment.id)}
                                                        className="text-blue-600 hover:text-blue-900"
                                                    >
                                                        View
                                                    </button>
                                                    <button
                                                        onClick={() => handleGenerateReceipt(payment.id)}
                                                        className="text-green-600 hover:text-green-900"
                                                    >
                                                        Receipt
                                                    </button>
                                                </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {safePayments.data.length === 0 && (
                    <div className="text-center py-12">
                        <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No payments yet</h3>
                        <p className="mt-1 text-sm text-gray-500">Payments from your property bookings will appear here.</p>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}
