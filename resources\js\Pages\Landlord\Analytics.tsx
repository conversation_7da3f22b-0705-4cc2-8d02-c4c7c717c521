import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    ChartBarIcon,
    EyeIcon,
    HeartIcon,
    CalendarIcon,
    CurrencyDollarIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';

interface AnalyticsData {
    property_views: number;
    total_bookings: number;
    conversion_rate: number;
    total_revenue: number;
    average_rating: number;
    total_favorites: number;
}

interface AnalyticsPageProps extends PageProps {
    analytics: AnalyticsData;
}

export default function Analytics({ auth, analytics }: AnalyticsPageProps) {
    // Fallback for undefined analytics
    const safeAnalytics = analytics || {
        property_views: 0,
        total_bookings: 0,
        conversion_rate: 0,
        total_revenue: 0,
        average_rating: 0,
        total_favorites: 0,
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const formatPercentage = (value: number) => {
        return `${value.toFixed(1)}%`;
    };

    // Mock data for charts (in a real app, this would come from the backend)
    const monthlyData = [
        { month: 'Jan', views: 120, bookings: 8, revenue: 2400 },
        { month: 'Feb', views: 150, bookings: 12, revenue: 3600 },
        { month: 'Mar', views: 180, bookings: 15, revenue: 4500 },
        { month: 'Apr', views: 200, bookings: 18, revenue: 5400 },
        { month: 'May', views: 220, bookings: 22, revenue: 6600 },
        { month: 'Jun', views: 250, bookings: 25, revenue: 7500 },
    ];

    const topProperties = [
        { name: 'Modern Downtown Apartment', views: 450, bookings: 28, revenue: 8400 },
        { name: 'Cozy Suburban House', views: 380, bookings: 22, revenue: 6600 },
        { name: 'Luxury Penthouse Suite', views: 320, bookings: 18, revenue: 9000 },
        { name: 'Beach View Condo', views: 290, bookings: 15, revenue: 4500 },
    ];

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
                        <p className="text-gray-600">Property performance analytics and insights</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>Last 30 days</option>
                            <option>Last 3 months</option>
                            <option>Last 6 months</option>
                            <option>Last year</option>
                        </select>
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                            Export Report
                        </button>
                    </div>
                </div>
            }
        >
            <Head title="Analytics - Landlord" />

            <div className="space-y-6">
                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-100">
                                <EyeIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Views</p>
                                <p className="text-2xl font-bold text-gray-900">{safeAnalytics.property_views.toLocaleString()}</p>
                                {/* Removed hardcoded trend */}
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-green-100">
                                <CalendarIcon className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Bookings</p>
                                <p className="text-2xl font-bold text-gray-900">{safeAnalytics.total_bookings}</p>
                                {/* Removed hardcoded trend */}
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-purple-100">
                                <ChartBarIcon className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Conversion Rate</p>
                                <p className="text-2xl font-bold text-gray-900">{formatPercentage(safeAnalytics.conversion_rate)}</p>
                                {/* Removed hardcoded trend */}
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-yellow-100">
                                <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p className="text-2xl font-bold text-gray-900">{formatCurrency(safeAnalytics.total_revenue)}</p>
                                {/* Removed hardcoded trend */}
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-red-100">
                                <HeartIcon className="h-6 w-6 text-red-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Favorites</p>
                                <p className="text-2xl font-bold text-gray-900">{safeAnalytics.total_favorites}</p>
                                {/* Removed hardcoded trend */}
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-indigo-100">
                                <ChartBarIcon className="h-6 w-6 text-indigo-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Avg. Rating</p>
                                <p className="text-2xl font-bold text-gray-900">{safeAnalytics.average_rating.toFixed(1)}</p>
                                {/* Removed hardcoded trend */}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Charts Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Monthly Performance Chart */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-6">Monthly Performance</h3>
                        {monthlyData.length > 0 ? (
                            <div className="space-y-4">
                                {monthlyData.map((data, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-12 text-sm text-gray-600">{data.month}</div>
                                            <div className="flex-1 mx-4">
                                                <div className="bg-gray-200 rounded-full h-2">
                                                    <div
                                                        className="bg-blue-600 h-2 rounded-full"
                                                        style={{ width: `${(data.views / Math.max(...monthlyData.map(d => d.views))) * 100}%` }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-sm text-gray-900 w-16 text-right">{data.views} views</div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500 text-sm">No monthly data available.</div>
                        )}
                    </div>

                    {/* Top Performing Properties */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-6">Top Performing Properties</h3>
                        {topProperties.length > 0 ? (
                            <div className="space-y-4">
                                {topProperties.map((property, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                                        <div className="flex items-center">
                                            <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center mr-3">
                                                <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                                            </div>
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">{property.name}</div>
                                                <div className="text-sm text-gray-500">{property.views} views • {property.bookings} bookings</div>
                                            </div>
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">{formatCurrency(property.revenue)}</div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500 text-sm">No top performing properties to display.</div>
                        )}
                    </div>
                </div>

                {/* Performance Insights */}
                <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-6">Performance Insights</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                            <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                            <h4 className="text-sm font-medium text-gray-900">Peak Viewing Hours</h4>
                            <p className="text-sm text-gray-600 mt-1">Most views occur between 7-9 PM on weekdays</p>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                            <CalendarIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                            <h4 className="text-sm font-medium text-gray-900">Best Booking Days</h4>
                            <p className="text-sm text-gray-600 mt-1">Weekends show 40% higher booking rates</p>
                        </div>
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                            <ChartBarIcon className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                            <h4 className="text-sm font-medium text-gray-900">Optimization Tip</h4>
                            <p className="text-sm text-gray-600 mt-1">Add more photos to increase conversion by 25%</p>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
