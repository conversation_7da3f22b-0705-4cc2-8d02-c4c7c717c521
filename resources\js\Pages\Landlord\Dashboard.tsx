import React, { useEffect } from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Head, Link } from '@inertiajs/react';
import { PageProps } from '@/types';
import { gsap } from 'gsap';
import {
    HomeIcon,
    CalendarIcon,
    CurrencyDollarIcon,
    ChatBubbleLeftIcon,
    StarIcon,
    CheckCircleIcon,
    ChartBarIcon,
    ClockIcon,
    EyeIcon,
    ArrowUpIcon,
    ArrowDownIcon,
    PlusIcon
} from '@heroicons/react/24/outline';

interface Property {
    id: number;
    title: string;
    type: string;
    price: number;
    city: string;
    status: string;
    views: number;
    bookings_count: number;
    favorites_count: number;
    reviews_count: number;
    reviews_avg_rating: number;
    primary_image?: {
        image_path: string;
    };
}

interface DashboardProps extends PageProps {
    stats: {
        total_properties: number;
        active_properties: number;
        total_bookings: number;
        pending_bookings: number;
        total_revenue: number;
        monthly_revenue: number;
        unread_messages: number;
        pending_reviews: number;
    };
    properties: Property[];
    monthlyRevenue: Array<{
        year: number;
        month: number;
        total: number;
    }>;
    recentBookings: Array<{
        id: number;
        type: string;
        status: string;
        user: {
            name: string;
        };
        property: {
            title: string;
        };
        created_at: string;
    }>;
    recentPayments: Array<{
        id: number;
        amount: number;
        status: string;
        payment_type: string;
        user: {
            name: string;
        };
        property: {
            title: string;
        };
        created_at: string;
    }>;
    recentMessages: Array<{
        id: number;
        message: string;
        sender: {
            name: string;
        };
        property?: {
            title: string;
        };
        created_at: string;
    }>;
}

export default function LandlordDashboard({
    auth,
    stats,
    properties,
    monthlyRevenue,
    recentBookings,
    recentPayments,
    recentMessages
}: DashboardProps) {
    useEffect(() => {
        // Animate dashboard elements
        gsap.fromTo('.dashboard-card',
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" }
        );

        gsap.fromTo('.activity-item',
            { x: -20, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.4, stagger: 0.05, delay: 0.3, ease: "power2.out" }
        );

        gsap.fromTo('.property-card',
            { scale: 0.95, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.5, stagger: 0.1, delay: 0.4, ease: "power2.out" }
        );
    }, []);
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'confirmed': return 'bg-green-100 text-green-800';
            case 'paid': return 'bg-green-100 text-green-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'failed': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const statCards = [
        {
            title: 'Total Properties',
            value: stats.total_properties,
            icon: HomeIcon,
            color: 'bg-gradient-to-r from-blue-500 to-blue-600',
            change: null, // Removed hardcoded change
            trend: null, // Removed hardcoded trend
            subtitle: `${stats.active_properties} active`
        },
        {
            title: 'Total Bookings',
            value: stats.total_bookings,
            icon: CalendarIcon,
            color: 'bg-gradient-to-r from-purple-500 to-purple-600',
            change: null,
            trend: null,
            subtitle: `${stats.pending_bookings} pending`
        },
        {
            title: 'Monthly Revenue',
            value: formatCurrency(stats.monthly_revenue),
            icon: CurrencyDollarIcon,
            color: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
            change: null,
            trend: null,
            subtitle: 'This month'
        },
        {
            title: 'Total Revenue',
            value: formatCurrency(stats.total_revenue),
            icon: ChartBarIcon,
            color: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
            change: null,
            trend: null,
            subtitle: 'All time'
        },
        {
            title: 'Unread Messages',
            value: stats.unread_messages,
            icon: ChatBubbleLeftIcon,
            color: 'bg-gradient-to-r from-pink-500 to-pink-600',
            change: null,
            trend: null,
            subtitle: 'New inquiries'
        },
        {
            title: 'Pending Reviews',
            value: stats.pending_reviews,
            icon: StarIcon,
            color: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
            change: null,
            trend: null,
            subtitle: 'Awaiting response'
        },
    ];

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Landlord Dashboard</h1>
                        <p className="text-gray-600">Welcome back, {auth.user.name}</p>
                    </div>
                    <Link
                        href="/properties/create"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center hover:scale-105 active:scale-95 transition-all duration-200 shadow-md hover:shadow-lg mr-4"
                    >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Property
                    </Link>
                </div>
            }
        >
            <Head title="Landlord Dashboard - PropertyHub" />

            <div className="space-y-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
                {statCards.map((stat, index) => (
                    <div key={index} className="dashboard-card bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                        <div className="p-6">
                            <div className="flex items-center">
                                <div className={`flex-shrink-0 p-3 rounded-lg ${stat.color} shadow-md`}>
                                    <stat.icon className="h-6 w-6 text-white" />
                                </div>
                                <div className="ml-4 flex-1">
                                    <p className="text-sm font-medium text-gray-500 truncate">
                                        {stat.title}
                                    </p>
                                    <div className="flex items-center">
                                        <p className="text-2xl font-bold text-gray-900">
                                            {stat.value}
                                        </p>
                                        {stat.change && (
                                            <div className={`ml-2 flex items-center text-sm ${
                                                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                                            }`}>
                                                {stat.trend === 'up' ? (
                                                    <ArrowUpIcon className="h-4 w-4 mr-1" />
                                                ) : (
                                                    <ArrowDownIcon className="h-4 w-4 mr-1" />
                                                )}
                                                {stat.change}
                                            </div>
                                        )}
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        {stat.subtitle}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
                </div>

                {/* Properties Grid */}
                <div>
                <div className="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow duration-300">
                    <div className="p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                <HomeIcon className="h-5 w-5 mr-2 text-blue-600" />
                                Your Properties
                            </h3>
                            <Link
                                href="/properties"
                                className="text-blue-600 hover:text-blue-800 text-sm font-medium hover:underline transition-colors"
                            >
                                View All
                            </Link>
                        </div>
                        <div className="property-grid-3">
                            {properties.map((property) => (
                                <div key={property.id} className="property-card border rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                                    <div className="aspect-w-16 aspect-h-9 bg-gray-200 relative">
                                        {property.primary_image ? (
                                            <img
                                                src={`/storage/${property.primary_image.image_path}`}
                                                alt={property.title}
                                                className="w-full h-48 object-cover"
                                            />
                                        ) : (
                                            <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                                <HomeIcon className="h-12 w-12 text-gray-400" />
                                            </div>
                                        )}
                                        <div className={`absolute top-2 right-2 px-2 py-1 text-xs rounded-full font-medium ${
                                            property.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                                        }`}>
                                            {property.status}
                                        </div>
                                    </div>
                                    <div className="p-4">
                                        <h4 className="font-semibold text-gray-900 truncate">{property.title}</h4>
                                        <p className="text-sm text-gray-500 capitalize">{property.type} • {property.city}</p>
                                        <p className="text-lg font-bold text-gray-900 mt-2">
                                            {formatCurrency(property.price)}
                                        </p>
                                        <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                                            <span className="flex items-center bg-gray-50 px-2 py-1 rounded">
                                                <EyeIcon className="h-3 w-3 mr-1" />
                                                {property.views}
                                            </span>
                                            <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded">{property.bookings_count} bookings</span>
                                            <span className="bg-pink-50 text-pink-700 px-2 py-1 rounded">{property.favorites_count} ♥</span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
                </div>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Recent Bookings */}
                <div className="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow duration-300">
                    <div className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <CalendarIcon className="h-5 w-5 mr-2 text-blue-600" />
                            Recent Bookings
                        </h3>
                        {recentBookings.length > 0 ? (
                            <div className="space-y-3">
                                {recentBookings.map((booking) => (
                                    <div key={booking.id} className="activity-item border-l-4 border-blue-400 pl-3 py-2 hover:bg-gray-50 rounded-r-lg transition-colors">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {booking.property.title}
                                        </p>
                                        <p className="text-xs text-gray-500 capitalize">
                                            {booking.type} by {booking.user.name} • {formatDate(booking.created_at)}
                                        </p>
                                        <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${getStatusColor(booking.status)}`}>
                                            {booking.status}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500 text-sm">No recent bookings.</div>
                        )}
                    </div>
                </div>

                {/* Recent Payments */}
                <div className="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow duration-300">
                    <div className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <CurrencyDollarIcon className="h-5 w-5 mr-2 text-green-600" />
                            Recent Payments
                        </h3>
                        {recentPayments.length > 0 ? (
                            <div className="space-y-3">
                                {recentPayments.map((payment) => (
                                    <div key={payment.id} className="activity-item border-l-4 border-green-400 pl-3 py-2 hover:bg-gray-50 rounded-r-lg transition-colors">
                                        <p className="text-sm font-medium text-gray-900">
                                            {formatCurrency(payment.amount)}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                            {payment.payment_type} • {payment.property.title}
                                        </p>
                                        <p className="text-xs text-gray-400">
                                            by {payment.user.name} • {formatDate(payment.created_at)}
                                        </p>
                                        <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${getStatusColor(payment.status)}`}>
                                            {payment.status}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500 text-sm">No recent payments.</div>
                        )}
                    </div>
                </div>

                {/* Recent Messages */}
                <div className="bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow duration-300">
                    <div className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <ChatBubbleLeftIcon className="h-5 w-5 mr-2 text-purple-600" />
                            Recent Messages
                        </h3>
                        {recentMessages.length > 0 ? (
                            <div className="space-y-3">
                                {recentMessages.map((message) => (
                                    <div key={message.id} className="activity-item border-l-4 border-purple-400 pl-3 py-2 hover:bg-gray-50 rounded-r-lg transition-colors">
                                        <p className="text-sm font-medium text-gray-900">
                                            From {message.sender.name}
                                        </p>
                                        <p className="text-xs text-gray-500 truncate">
                                            {message.message}
                                        </p>
                                        <p className="text-xs text-gray-400">
                                            {message.property?.title || 'General inquiry'} • {formatDate(message.created_at)}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500 text-sm">No recent messages.</div>
                        )}
                    </div>
                </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
