import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    CalendarIcon,
    UserIcon,
    HomeIcon,
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

interface Booking {
    id: number;
    type: string;
    status: string;
    check_in: string;
    check_out: string;
    total_amount: number;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
    };
    property: {
        id: number;
        title: string;
        city: string;
        primary_image?: {
            image_path: string;
        };
    };
}

interface BookingsPageProps extends PageProps {
    bookings: {
        data: Booking[];
        links: any[];
        meta: any;
    };
}

export default function Bookings({ auth, bookings }: BookingsPageProps) {
    // Fallback for undefined bookings
    const safeBookings = bookings || { data: [], meta: { total: 0 }, links: [] };

    const handleViewBooking = (bookingId: number) => {
        // Implement navigation to a detailed booking view or open a modal
        console.log('View booking:', bookingId);
        // Example: router.visit(`/landlord/bookings/${bookingId}`);
    };

    const handleUpdateBookingStatus = async (bookingId: number, status: 'confirmed' | 'cancelled') => {
        if (confirm(`Are you sure you want to ${status} this booking?`)) {
            try {
                const response = await fetch(`/api/bookings/${bookingId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                    body: JSON.stringify({ status }),
                });

                if (response.ok) {
                    window.location.reload(); // Reload to reflect changes
                } else {
                    const errorData = await response.json();
                    alert(`Failed to update booking status: ${errorData.message || response.statusText}`);
                }
            } catch (error) {
                console.error('Error updating booking status:', error);
                alert('An error occurred while updating the booking status.');
            }
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'confirmed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'completed':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'confirmed':
                return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
            case 'pending':
                return <ClockIcon className="h-5 w-5 text-yellow-600" />;
            case 'cancelled':
                return <XCircleIcon className="h-5 w-5 text-red-600" />;
            case 'completed':
                return <CheckCircleIcon className="h-5 w-5 text-blue-600" />;
            default:
                return <ExclamationTriangleIcon className="h-5 w-5 text-gray-600" />;
        }
    };

    const getBookingStats = () => {
        const total = safeBookings.data.length;
        const confirmed = safeBookings.data.filter(b => b.status === 'confirmed').length;
        const pending = safeBookings.data.filter(b => b.status === 'pending').length;
        const cancelled = safeBookings.data.filter(b => b.status === 'cancelled').length;

        return { total, confirmed, pending, cancelled };
    };

    const stats = getBookingStats();

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
                        <p className="text-gray-600">Manage bookings for your properties</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search bookings..."
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                    </div>
                </div>
            }
        >
            <Head title="My Bookings - Landlord" />

            <div className="space-y-6">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-100">
                                <CalendarIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Bookings</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-green-100">
                                <CheckCircleIcon className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Confirmed</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.confirmed}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-yellow-100">
                                <ClockIcon className="h-6 w-6 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Pending</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-red-100">
                                <XCircleIcon className="h-6 w-6 text-red-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Cancelled</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.cancelled}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Bookings Table */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">Recent Bookings</h3>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Property & Guest
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Dates
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {safeBookings.data.map((booking) => (
                                    <tr key={booking.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                                                    {booking.property.primary_image ? (
                                                        <img
                                                            src={`/storage/${booking.property.primary_image.image_path}`}
                                                            alt={booking.property.title}
                                                            className="h-12 w-12 rounded-lg object-cover"
                                                        />
                                                    ) : (
                                                        <HomeIcon className="h-6 w-6 text-gray-400" />
                                                    )}
                                                </div>
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{booking.property.title}</div>
                                                    <div className="text-sm text-gray-500">{booking.property.city}</div>
                                                    <div className="text-sm text-gray-500 flex items-center mt-1">
                                                        <UserIcon className="h-4 w-4 mr-1" />
                                                        {booking.user.name}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                {formatDate(booking.check_in)} - {formatDate(booking.check_out)}
                                            </div>
                                            <div className="text-sm text-gray-500">{booking.type}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                {getStatusIcon(booking.status)}
                                                <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                                                    {booking.status}
                                                </span>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {formatCurrency(booking.total_amount)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex space-x-2">
                                                    <button
                                                        onClick={() => handleViewBooking(booking.id)}
                                                        className="text-blue-600 hover:text-blue-900"
                                                    >
                                                        View
                                                    </button>
                                                    {booking.status === 'pending' && (
                                                        <>
                                                            <button
                                                                onClick={() => handleUpdateBookingStatus(booking.id, 'confirmed')}
                                                                className="text-green-600 hover:text-green-900"
                                                            >
                                                                Approve
                                                            </button>
                                                            <button
                                                                onClick={() => handleUpdateBookingStatus(booking.id, 'cancelled')}
                                                                className="text-red-600 hover:text-red-900"
                                                            >
                                                                Decline
                                                            </button>
                                                        </>
                                                    )}
                                                </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {safeBookings.data.length === 0 && (
                    <div className="text-center py-12">
                        <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
                        <p className="mt-1 text-sm text-gray-500">Your property bookings will appear here once guests start booking.</p>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}
