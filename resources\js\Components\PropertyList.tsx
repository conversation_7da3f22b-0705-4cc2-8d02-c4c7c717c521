import React, { useState, useEffect } from 'react';
import PropertyCard from './PropertyCard';
import PropertyFilters from './PropertyFilters';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface Property {
    id: number;
    title: string;
    description: string;
    type: string;
    listing_type: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    area?: number;
    address: string;
    city: string;
    state: string;
    primary_image?: {
        image_path: string;
        alt_text?: string;
    };
    user: {
        name: string;
    };
    is_featured: boolean;
    created_at: string;
}

interface PaginationData {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    data: Property[];
}

interface Filters {
    search?: string;
    type?: string;
    listing_type?: string;
    min_price?: number;
    max_price?: number;
    bedrooms?: number;
    bathrooms?: number;
    city?: string;
    featured?: boolean;
    sort_by?: string;
    sort_order?: string;
}

interface PropertyListProps {
    initialProperties?: PaginationData;
    showFilters?: boolean;
    title?: string;
    limit?: number;
}

const PropertyList: React.FC<PropertyListProps> = ({
    initialProperties,
    showFilters = true,
    title = "Properties",
    limit
}) => {
    const [properties, setProperties] = useState<PaginationData | null>(initialProperties || null);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState<Filters>({});
    const [favorites, setFavorites] = useState<number[]>([]);

    const fetchProperties = async (newFilters: Filters = {}, page: number = 1) => {
        setLoading(true);
        try {
            const params = new URLSearchParams();

            Object.entries({ ...filters, ...newFilters }).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    params.append(key, value.toString());
                }
            });

            params.append('page', page.toString());
            if (limit) {
                params.append('per_page', limit.toString());
            }

            const response = await fetch(`/api/properties?${params.toString()}`);
            const data = await response.json();
            setProperties(data);
        } catch (error) {
            console.error('Error fetching properties:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (newFilters: Filters) => {
        setFilters(newFilters);
        fetchProperties(newFilters);
    };

    const handlePageChange = (page: number) => {
        fetchProperties(filters, page);
    };

    const toggleFavorite = async (propertyId: number) => {
        try {
            const isFavorite = favorites.includes(propertyId);
            const method = isFavorite ? 'DELETE' : 'POST';
            const url = isFavorite ? `/api/favorites/${propertyId}` : '/api/favorites';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: isFavorite ? undefined : JSON.stringify({ property_id: propertyId }),
            });

            if (response.ok) {
                if (isFavorite) {
                    setFavorites(favorites.filter(id => id !== propertyId));
                } else {
                    setFavorites([...favorites, propertyId]);
                }
            } else {
                console.error('Failed to toggle favorite status:', response.statusText);
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
        }
    };

    useEffect(() => {
        if (!initialProperties) {
            fetchProperties();
        }
    }, []);

    return (
        <div className="w-full">
            {title && (
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
                </div>
            )}

            {showFilters && (
                <div className="mb-8">
                    <PropertyFilters onFilterChange={handleFilterChange} />
                </div>
            )}

            {loading ? (
                <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            ) : properties && properties.data.length > 0 ? (
                <>
                    <div className="property-grid mb-8">
                        {properties.data.map((property) => (
                            <PropertyCard
                                key={property.id}
                                property={property}
                                isFavorite={favorites.includes(property.id)}
                                onToggleFavorite={toggleFavorite}
                            />
                        ))}
                    </div>

                    {/* Pagination */}
                    {properties.last_page > 1 && (
                        <div className="flex justify-center items-center space-x-2 mt-8">
                            <button
                                onClick={() => handlePageChange(properties.current_page - 1)}
                                disabled={properties.current_page === 1}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                            >
                                Previous
                            </button>

                            <span className="px-4 py-2 text-sm text-gray-700">
                                Page {properties.current_page} of {properties.last_page}
                            </span>

                            <button
                                onClick={() => handlePageChange(properties.current_page + 1)}
                                disabled={properties.current_page === properties.last_page}
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                            >
                                Next
                            </button>
                        </div>
                    )}
                </>
            ) : (
                <div className="text-center py-12">
                    <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No properties found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        Try adjusting your search criteria or filters.
                    </p>
                </div>
            )}
        </div>
    );
};

export default PropertyList;
